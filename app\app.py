import os
import json
from datetime import datetime, timedelta

from mcp.server import <PERSON><PERSON><PERSON>
from service.dict_service import list_all_active, query_dict
from service.api_service import ApiService, AutoTaskLogParams
from util.utils import get_env
from service.es_service import query_message

app = FastMCP("bc-mcp-server")


@app.tool()
async def list_all_dict() -> str:
    """
    列出所有的数据字典

    Returns:
        所有的数据字典
    """
    dicts = list_all_active()
    return "\n".join(["id:" + str(d.id) + ",字典名称:" + d.file_name for d in dicts])


@app.tool()
async def query_dict_by_id(dict_id: int) -> str:
    """
    根据ID查询对应的数据字典详情
    Args:
        dict_id: 数据字典的ID
    Returns:
        数据字典详情
    """
    d = query_dict(dict_id)
    file_path = os.path.join("./doc", d.file_path.lstrip("/"))
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            return file.read()
    except FileNotFoundError:
        return "指定的文件未找到"
    except Exception as e:
        return f"读取文件时发生错误: {str(e)}"


@app.tool()
async def query_auto_task_logs(
        trace_key: str,
        current_page: int = 1,
        page_size: int = 10,
        start_time_start: str = '',
        start_time_end: str = '',
        env: str = "prod",
) -> str:
    """
    查询自动任务日志

    Args:
        trace_key: 单号，如果包含@符号，则自动截取@前字符串作为单号
        current_page: 当前页，从1开始
        page_size: 每页显示数量
        start_time_start: 搜索的起始时间 (选填，格式: YYYY-MM-DD)
        start_time_end: 搜索的结束时间 (选填，格式: YYYY-MM-DD)
        env: 环境选择，分为dev（测试环境）和prod（生产环境），默认prod

    Returns:
        任务日志数据的JSON字符串。详细描述如下：
            autoTraceId (String)：车险任务唯一标识
            actionLogs (String)：操作日志，主要包括
                actionLogId: 操作日志ID
                template：执行的模板
            applyJson (String)：任务原始数据（JSON字符串，可为空）
            endTime (LocalDateTime)：任务结束时间（yyyy-MM-dd HH:mm:ss）
            companyId (String)：保险公司ID
            plateNo (String)：车牌号
            resultStr (String)：任务执行结果，包含多层级数据，主要包括：
                flowId：流程ID
                businessId：业务ID
                callBackUrl：回调地址
                ruleResponse：规则引擎返回内容
                platformData：平台回写数据
            startTime (LocalDateTime)：任务开始时间（yyyy-MM-dd HH:mm:ss）
            taskStatus (String)：任务状态
            taskType (String)：任务类型（含渠道和业务类型）
            traceKey (String)：任务追踪关键字
            feedbackJson (String)：响应数据（JSON字符串，可为空）
            bizProposeNo (String)：商业投保单号（可为空）
            efcProposeNo (String)：交强险投保单号（可为空）
            bizPolicyNo (String)：商业保单号（可为空）
            efcPolicyNo (String)：交强险保单号（可为空）
            failureCause (String)：失败原因（可为空）
            failureCauseCategory (String)：失败原因分类（可为空）
            requestSourceId (Integer)：请求来源ID
            internetSales (Integer)：是否互联网销售（0-否，1-是）
            sourceKind (Integer)：来源类型（可为空）
            vin (String)：车辆VIN码（可为空）
            dataSourceLogId (Integer)：数据源日志ID（可为空）
            补充说明：
            resultStr 字段为多层级结构，包含流程信息（flowId）、业务参数（applyJson）、规则引擎调用（ruleItem.runningRules）、平台回写结果（policyStartTime/policyEndTime）、车辆详细信息（vehicleInfo.tradeModelCode）等。

    """
    config = get_env()[env]
    current_date = datetime.now()
    if start_time_end == '':
        start_time_end = current_date.strftime('%Y-%m-%d')
    if start_time_start == '':
        start_time_start = (current_date - timedelta(days=2)).strftime('%Y-%m-%d')

    # 如果trace_key包含@符号，则自动截取@前字符串作为单号
    if '@' in trace_key:
        trace_key = trace_key.split('@')[0]
    # Create params object
    params = AutoTaskLogParams(
        traceKey=trace_key,
        startTimeStartTime=start_time_start,
        startTimeEndTime=start_time_end + ' 23:59:59',
        currentPage=current_page,
        pageSize=page_size,
    )

    # Initialize API service and query logs
    with ApiService(host=config["host"], username=config["username"], password=config["password"]) as api:
        result = api.query_auto_task_log(params)

    # 1. 判断 success
    if not result.get("success", False):
        result["restMsg"] = "查询失败"
        return json.dumps(result, ensure_ascii=False, indent=2)

    # 2. 判断 total
    rest_context = result.get("restContext", {})
    if rest_context.get("total", 0) == 0:
        result["restMsg"] = "未查询到结果"
        return json.dumps(result, ensure_ascii=False, indent=2)

    # 3. 处理 records
    for record in rest_context.get("records", []):
        # resultStr 拆分
        if "resultStr" in record and isinstance(record["resultStr"], str):
            record["resultStr"] = record["resultStr"].split("\n")
        # actionLogs 拆分
        if record.get("actionLogs"):
            logs = []
            for item in record["actionLogs"].split(","):
                if "@" in item:
                    id_, template = item.split("@", 1)
                    logs.append({"actionLogId": id_, "template": template})
                else:
                    logs.append({"actionLogId": item, "template": ""})
            record["actionLogs"] = logs

    return json.dumps(result, ensure_ascii=False, indent=2)


@app.tool()
async def query_auto_task_apply_json(auto_trace_id: str, env: str = "prod") -> str:
    """
    根据autoTraceId获取原始任务内容
    Args:
        auto_trace_id: 车险任务唯一标识
        env: 环境选择，分为dev（测试环境）和prod（生产环境），默认prod
    Returns:
        applyJson字段内容（JSON字符串，可为空）
    """
    config = get_env()[env]
    with ApiService(host=config["host"], username=config["username"], password=config["password"]) as api:
        result = api.query_auto_task_applyJson(auto_trace_id)
    return result or ""


@app.tool()
async def query_auto_task_feedback_json(auto_trace_id: str, env: str = "prod") -> str:
    """
    根据autoTraceId获取回写内容
    Args:
        auto_trace_id: 车险任务唯一标识
        env: 环境选择，分为dev（测试环境）和prod（生产环境），默认prod
    Returns:
        feedbackJson字段内容（JSON字符串，可为空）
    """
    config = get_env()[env]
    with ApiService(host=config["host"], username=config["username"], password=config["password"]) as api:
        result = api.query_auto_task_feedbackJson(auto_trace_id)
    return result or ""


@app.tool()
async def query_action_log(action_log_id: str, env: str = "prod") -> str:
    """
    根据actionLogId获取操作日志详情
    Args:
        action_log_id: 操作日志ID
        env: 环境选择，分为dev（测试环境）和prod（生产环境），默认prod
    Returns:
        操作日志详情的JSON字符串。字段说明如下：
            _id (String)：操作日志唯一标识
            autoTraceId (String)：车险任务唯一标识
            actionName (String)：模板名称（如：edi-2011-car_model_query）
            url (String)：模版执行的请求地址
            requestBody (String)：请求报文
            responseBody (String)：响应报文
            inTaskBody (String)：执行模板的传入参数
            outTaskBody (String)：执行模板的传出参数
            sendTime (String)：发送时间（格式：yyyy-MM-dd HH:mm:ss）
            receiveTime (String)：响应时间（格式：yyyy-MM-dd HH:mm:ss）
            exceptionInfo (String)：异常信息（如有异常则返回，否则为null）
    """
    config = get_env()[env]
    with ApiService(host=config["host"], username=config["username"], password=config["password"]) as api:
        result = api.query_action_log(action_log_id)
    return json.dumps(result, ensure_ascii=False, indent=2)


@app.tool()
async def query_server_log(flow_id: str, env: str = "prod") -> str:
    """
    根据flowId查询服务器的异常日志
    Args:
        flow_id: 流程ID
        env: 环境选择，分为dev（测试环境）和prod（生产环境），默认prod
    Returns:
        服务异常日志字符串
    """
    config = get_env()[env]
    resp = query_message(
        host=config['es_host'],
        msg=flow_id,
        size=500,
        days=2.0,
        index_name=config['es_river_index'],
    )
    result = ''
    if resp["total"]["value"] > 0:
        for hit in resp["hits"]:
            result += hit["_source"]["message"]
    else:
        result = "未查询到结果"
    return result
