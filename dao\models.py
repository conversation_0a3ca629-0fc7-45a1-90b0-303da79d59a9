from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

# Create Base class for declarative models
Base = declarative_base()


class BcDictIndex(Base):
    """数据字典目录"""
    __tablename__ = "bc_dict_index"

    id = Column(Integer, primary_key=True, index=True)
    file_name = Column(String(100), nullable=False)
    file_path = Column(Text, nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<BcDictIndex(id={self.id}, file_name={self.file_path}, file_path={self.file_path})>"
