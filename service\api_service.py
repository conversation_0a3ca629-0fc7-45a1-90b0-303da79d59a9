import logging
from typing import Dict, Any, Optional
from datetime import datetime

import httpx
from pydantic import BaseModel, Field, field_validator


class AutoTaskLogParams(BaseModel):
    """
    Parameters for querying auto task logs.
    """

    traceKey: Optional[str] = Field(default=None, description="Trace key for the task")
    dataSourceId: int = Field(default=0, description="Data source ID")
    sourceKind: Optional[str] = Field(default=None, description="Source kind")
    startTimeStartTime: str = Field(
        description="Start time for filtering (format: YYYY-MM-DD)"
    )
    startTimeEndTime: str = Field(
        description="End time for filtering (format: YYYY-MM-DD HH:MM:SS)"
    )
    currentPage: int = Field(default=1, description="当前页，从1开始")
    pageSize: int = Field(default=10, description="每页显示数量")

    @field_validator("startTimeStartTime", "startTimeEndTime")
    @classmethod
    def validate_date_format(cls, v: str) -> str:
        """
        Validate that the date strings are in the correct format.
        """
        if not v:
            return v

        try:
            if len(v) <= 10:  # YYYY-MM-DD
                datetime.strptime(v, "%Y-%m-%d")
            else:  # YYYY-MM-DD HH:MM:SS
                datetime.strptime(v, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise ValueError(
                f"Invalid date format: {v}. Expected format: YYYY-MM-DD or YYYY-MM-DD HH:MM:SS"
            )

        return v


class InterfaceQueryParams(BaseModel):
    """
    Parameters for querying interface configurations.
    """

    comId: Optional[str] = Field(default=None, description="Company ID")
    intType: Optional[str] = Field(
        default=None, description="Interface type (e.g., 'edi')"
    )
    action: Optional[str] = Field(
        default=None, description="Action type (e.g., 'quote')"
    )
    current: int = Field(default=1, description="Current page number, starting from 1")
    size: int = Field(default=10, description="Page size")


class TemplateQueryParams(BaseModel):
    """
    Parameters for querying template configurations.
    """

    name: Optional[str] = Field(
        default=None, description="Template name (supports wildcard with %)"
    )
    remark: Optional[str] = Field(
        default=None, description="Template remark (supports wildcard with %)"
    )
    current: int = Field(default=1, description="Current page number, starting from 1")
    size: int = Field(default=10, description="Page size")


class ApiService:
    current_user_path = "/userinfo/currentUser"
    login_path = "/userinfo/login"
    QUERY_AUTO_TASK_LOG_PATH = "/autoTaskLog"
    QUERY_AUTO_TASK_APPLY_JSON_PATH = "/autoTaskLog/getTaskBody/applyJson"
    QUERY_AUTO_TASK_FEEDBACK_JSON_PATH = "/autoTaskLog/getTaskBody/feedbackJson"
    QUERY_ACTION_LOG_PATH = "/autoTaskLog/getActionLog"
    INTERFACE_PATH = "/interface"

    def __init__(self, host: str, username: str, password: str):
        """
        Initialize the API service with host, username, and password.

        Args:
            host: The base URL of the API (e.g., "https://bc-admin.chetimes.com")
            username: The username for authentication
            password: The password for authentication
        """
        self.host = host
        self.username = username
        self.password = password
        self.client = httpx.Client(timeout=30.0, follow_redirects=True)
        self.is_logged_in = False
        self.login_response = None

    def login(self) -> Dict[str, Any]:
        """
        Login to the API and store the session cookies.

        Args:
            username: The username for authentication (defaults to self.username)
            password: The password for authentication (defaults to self.password)

        Returns:
            The parsed JSON response from the login request
        """

        resp = self.client.post(
            url=f"{self.host}{self.login_path}",
            data={
                "username": self.username,
                "password": self.password,
                "type": "account",
            },
        )

        resp.raise_for_status()
        data = resp.json()

        # Check if login was successful
        if data.get("success", True):
            self.is_logged_in = True
            self.login_response = data.get("restContext", {})
        else:
            self.is_logged_in = False
            logging.error(f"Login failed: {data.get('restMsg', 'Unknown error')}")

        return data

    def request(
        self,
        method: str,
        path: str,
        data: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        retry_on_auth_failure: bool = True,
    ) -> Dict[str, Any]:
        """
        Make an API request with automatic re-login if authentication fails.

        Args:
            method: The HTTP method (GET, POST, etc.)
            path: The API endpoint path
            data: Form data to send (for POST requests)
            json_data: JSON data to send (for POST requests)
            params: Query parameters (for GET requests)
            retry_on_auth_failure: Whether to retry the request after re-login

        Returns:
            The parsed JSON response
        """
        # Ensure user is logged in
        if not self.is_logged_in:
            self.login()

        # Make the request
        url = f"{self.host}{path}"
        resp = self.client.request(
            method=method, url=url, data=data, json=json_data, params=params
        )

        resp.raise_for_status()
        response_data = resp.json()

        # Check if authentication failed
        if (
            response_data.get("restCode") == -1
            and response_data.get("restMsg").startswith("请重新登陆！")
            and retry_on_auth_failure
        ):
            logging.info("Authentication failed. Attempting to re-login.")
            self.is_logged_in = False
            self.login()

            # Retry the request
            return self.request(
                method=method,
                path=path,
                data=data,
                json_data=json_data,
                params=params,
                retry_on_auth_failure=False,  # Prevent infinite loop
            )

        return response_data

    def current_user(self) -> Dict[str, Any]:
        """
        Get the current user information.

        Returns:
            The current user data
        """
        return self.request(method="GET", path=self.current_user_path)

    def query_auto_task_log(self, params: AutoTaskLogParams) -> Dict[str, Any]:
        """
        Query auto task logs with the given parameters.

        Args:
            params: AutoTaskLogParams object containing query parameters

        Returns:
            The parsed JSON response containing auto task logs
        """
        # Convert Pydantic model to dict and filter out None values
        query_params = {k: v for k, v in params.model_dump().items() if v is not None}
        # 确保分页参数存在且有效
        if "currentPage" not in query_params or query_params["currentPage"] < 1:
            query_params["currentPage"] = 1
        if "pageSize" not in query_params or query_params["pageSize"] < 1:
            query_params["pageSize"] = 10
        return self.request(
            method="GET", path=self.QUERY_AUTO_TASK_LOG_PATH, params=query_params
        )

    def query_auto_task_applyJson(self, autoTraceId: str):
        """
        根据autoTraceId获取applyJson内容。
        """
        path = f"{self.QUERY_AUTO_TASK_APPLY_JSON_PATH}/{autoTraceId}"
        resp = self.request(method="GET", path=path)
        return resp.get("applyJson")

    def query_auto_task_feedbackJson(self, autoTraceId: str):
        """
        根据autoTraceId获取feedbackJson内容。
        """
        path = f"{self.QUERY_AUTO_TASK_FEEDBACK_JSON_PATH}/{autoTraceId}"
        resp = self.request(method="GET", path=path)
        return resp.get("feedbackJson")

    def query_action_log(self, actionLogId: str):
        """
        根据actionLogId获取操作日志详情。
        """
        path = f"{self.QUERY_ACTION_LOG_PATH}/{actionLogId}"
        resp = self.request(method="GET", path=path)
        return resp

    def query_interface(
        self, params: Optional[InterfaceQueryParams] = None
    ) -> Dict[str, Any]:
        """
        Query interface configurations with the given parameters.

        Args:
            params: InterfaceQueryParams object containing query parameters.
                   If None, returns all interfaces.

        Returns:
            The parsed JSON response containing interface configurations
        """
        if params is None:
            # Query all interfaces
            return self.request(method="GET", path=self.INTERFACE_PATH)

        # Convert Pydantic model to dict and filter out None values
        query_params = {k: v for k, v in params.model_dump().items() if v is not None}

        # Ensure pagination parameters exist and are valid
        if "current" not in query_params or query_params["current"] < 1:
            query_params["current"] = 1
        if "size" not in query_params or query_params["size"] < 1:
            query_params["size"] = 10

        return self.request(method="GET", path=self.INTERFACE_PATH, params=query_params)

    def get_interface_by_id(self, interface_id: int) -> Dict[str, Any]:
        """
        Get interface configuration by ID.

        Args:
            interface_id: The interface ID to retrieve

        Returns:
            The parsed JSON response containing interface configuration details
        """
        path = f"{self.INTERFACE_PATH}/{interface_id}"
        return self.request(method="GET", path=path)

    def close(self):
        """Close the HTTP client session."""
        self.client.close()

    def __enter__(self):
        return self

    def __exit__(self, *_):
        self.close()
