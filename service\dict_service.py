from typing import List

from dao.database import get_db, SessionLocal
from dao.models import BcDictIndex


def list_all_active() -> List[BcDictIndex]:
    with SessionLocal() as db:
        return db.query(BcDictIndex).filter(BcDictIndex.is_active == True).all()


def query_dict(dict_id: int) -> BcDictIndex:
    with SessionLocal() as db:
        return db.query(BcDictIndex).filter(BcDictIndex.id == dict_id).first()
