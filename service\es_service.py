from datetime import datetime, timedelta

from elasticsearch import Elasticsearch


def query_message(host: str, msg: str, size: int, days: float, index_name: str, sort='desc'):
    # 获取当前时间
    now = datetime.now()
    now_str = now.strftime('%Y-%m-%dT%H:%M:%S.000Z')
    # 获取当前时间的前一天
    yesterday = now - timedelta(days=days)
    # 将日期转为字符串，格式2024-10-22T16:00:00.000Z
    yesterday_str = yesterday.strftime('%Y-%m-%dT%H:%M:%S.000Z')
    client = Elasticsearch(
        hosts=[host],
        # http_auth=(c['username'], c['password']),
        request_timeout=30,  # 请求超时时间
        connection_timeout=10,  # 连接超时时间
        max_retries=3,
        retry_on_timeout=True,
    )
    query = {
        "bool": {
            "must": [
                {
                    "match_phrase": {
                        "message": msg
                    }
                },
                # {
                #     "match_phrase": {
                #         "message": "[ERROR]"
                #     }
                # },
                {
                    "range": {
                        "@timestamp": {
                            "gte": yesterday_str,
                            "lt": now_str
                        }
                    }
                }
            ]
        }
    }
    sort = [
        {
            "@timestamp": {
                "order": sort
            }
        }
    ]
    _source = [
        "message"
    ]
    resp = client.search(index=index_name, query=query, sort=sort, _source=_source, from_=0, size=size)
    return resp['hits']
