import os
from dotenv import load_dotenv
from typing import Dict

load_dotenv()


def get_env() -> Dict[str, Dict[str, str]]:
    return {
        "dev": {
            "host": os.getenv("DEV_BC_ADMIN_HOST"),
            "username": os.getenv("DEV_BC_ADMIN_USERNAME"),
            "password": os.getenv("DEV_BC_ADMIN_PASSWORD"),
            "es_host": os.getenv('DEV_ES_HOST'),
            "es_river_index": os.getenv('DEV_ES_RIVER_INDEX'),
        },
        "prod": {
            "host": os.getenv("PROD_BC_ADMIN_HOST"),
            "username": os.getenv("PROD_BC_ADMIN_USERNAME"),
            "password": os.getenv("PROD_BC_ADMIN_PASSWORD"),
            "es_host": os.getenv('PROD_ES_HOST'),
            "es_river_index": os.getenv('PROD_ES_RIVER_INDEX'),
        }
    }
